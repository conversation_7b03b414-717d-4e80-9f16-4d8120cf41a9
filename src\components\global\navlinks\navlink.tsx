import {
  Link as ChakraLink,
  Text,
  LinkProps as ChakraLinkProps,
  HStack,
  Icon,
} from "@chakra-ui/react";
import { FC, ReactNode } from "react";
import { ActiveLink } from "./active-link";

interface NavLinkProps extends ChakraLinkProps {
  icon: ReactNode;
  children: ReactNode;
  shouldMatchExactHref?: boolean;
  href: string;
}

export const NavLink: FC<NavLinkProps> = ({
  icon,
  children,
  shouldMatchExactHref,
  href,
  ...rest
}) => {
  return (
    <ActiveLink
      href={href}
      passHref
      shouldMatchExactHref={shouldMatchExactHref}
    >
      <ChakraLink
        as="div"
        width="100%"
        maxH={11}
        p={{ lg: 4, md: 2 }}
        cursor="pointer"
        _focus={{ outline: 0 }}
        _hover={{
          textDecoration: "none",
          background: "#000000",
          borderRadius: "5px",
        }}
        _active={{
          textDecoration: "none",
          background: "#000000",
          borderRadius: "5px",
        }}
        {...rest}
      >
        <HStack
          maxH={11}
          width="100%"
          _hover={
            {
              // color: "blue.300",
            }
          }
        >
          {/* <Icon fontSize="28px" transition="200ms" color={"chatPrimary"}>
            {icon}
          </Icon> */}
          <Text
            fontSize={"20px"}
            fontWeight={"light"}
            height={11}
            color={"text"}
            transition="200ms"
            alignContent={"center"}
          >
            {children}
          </Text>
        </HStack>
      </ChakraLink>
    </ActiveLink>
  );
};
